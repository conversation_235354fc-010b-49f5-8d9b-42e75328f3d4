@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.lottery-container.data-v-557dc19a {
  min-height: 100vh;
  /* 背景色通过内联样式动态设置 */
  padding: 40rpx 30rpx;
  padding-bottom: 80rpx;
  /* 增加底部间距，确保内容不被遮挡 */
  box-sizing: border-box;
}
/* 加载状态样式 */
.loading-container.data-v-557dc19a {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  text-align: center;
}
.loading-container .loading-icon.data-v-557dc19a {
  font-size: 80rpx;
  margin-bottom: 30rpx;
  -webkit-animation: rotate-data-v-557dc19a 2s linear infinite;
          animation: rotate-data-v-557dc19a 2s linear infinite;
}
.loading-container .loading-text.data-v-557dc19a {
  font-size: 32rpx;
  color: rgba(255, 255, 255, 0.8);
}
@-webkit-keyframes rotate-data-v-557dc19a {
from {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
to {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
}
}
@keyframes rotate-data-v-557dc19a {
from {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
to {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
}
}
.merchant-header.data-v-557dc19a {
  text-align: center;
  margin-bottom: 60rpx;
}
.merchant-header .merchant-name.data-v-557dc19a {
  font-size: 36rpx;
  font-weight: bold;
  color: #fff;
  margin-bottom: 10rpx;
}
.merchant-header .activity-desc.data-v-557dc19a {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  padding: 0 20rpx;
}
.lottery-grid-container.data-v-557dc19a {
  display: flex;
  justify-content: center;
  margin-bottom: 40rpx;
}
.lottery-grid-container .grid-wrapper.data-v-557dc19a {
  width: 600rpx;
  height: 600rpx;
}
.lottery-grid-container .lottery-grid.data-v-557dc19a {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: repeat(3, 1fr);
  gap: 8rpx;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  padding: 20rpx;
  box-sizing: border-box;
}
.lottery-grid-container .grid-item.data-v-557dc19a {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 15rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}
.lottery-grid-container .grid-item.active.data-v-557dc19a {
  background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
  -webkit-transform: scale(1.05);
          transform: scale(1.05);
  box-shadow: 0 8rpx 25rpx rgba(255, 107, 107, 0.4);
}
.lottery-grid-container .grid-item.active .prize-item .prize-icon.data-v-557dc19a {
  -webkit-animation: bounce-data-v-557dc19a 0.6s ease-in-out;
          animation: bounce-data-v-557dc19a 0.6s ease-in-out;
}
.lottery-grid-container .grid-item.active .prize-item .prize-name.data-v-557dc19a {
  color: #fff;
  font-weight: bold;
}
.lottery-grid-container .grid-item.center.data-v-557dc19a {
  /* 背景色通过内联样式动态设置 */
  cursor: pointer;
}
.lottery-grid-container .grid-item.center.data-v-557dc19a:hover {
  -webkit-transform: scale(1.02);
          transform: scale(1.02);
}
.lottery-grid-container .grid-item.center.data-v-557dc19a:active {
  -webkit-transform: scale(0.98);
          transform: scale(0.98);
}
.lottery-grid-container .prize-item.data-v-557dc19a {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
  padding: 10rpx;
}
.lottery-grid-container .prize-item .prize-icon.data-v-557dc19a {
  font-size: 48rpx;
  margin-bottom: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60rpx;
  height: 60rpx;
}
.lottery-grid-container .prize-item .prize-icon .prize-image.data-v-557dc19a {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
}
.lottery-grid-container .prize-item .prize-icon .default-icon.data-v-557dc19a {
  font-size: 48rpx;
}
.lottery-grid-container .prize-item .prize-name.data-v-557dc19a {
  font-size: 24rpx;
  color: #333;
  line-height: 1.2;
  word-break: break-all;
}
.lottery-grid-container .center-button.data-v-557dc19a {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #fff;
}
.lottery-grid-container .center-button .center-text.data-v-557dc19a {
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}
.lottery-grid-container .center-button .remaining-text.data-v-557dc19a {
  font-size: 20rpx;
  opacity: 0.9;
}
/* 上次中奖记录显示样式 - 使用WinningModal样式 */
.last-winning-display.data-v-557dc19a {
  /* 背景色通过内联样式动态设置 */
  border-radius: 20rpx;
  padding: 60rpx 40rpx 40rpx;
  margin: 40rpx;
  max-width: 600rpx;
  width: 90%;
  position: relative;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);
}
.winning-animation.data-v-557dc19a {
  text-align: center;
  position: relative;
}
.fireworks.data-v-557dc19a {
  position: absolute;
  top: -20rpx;
  left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  width: 200rpx;
  height: 200rpx;
}
.firework.data-v-557dc19a {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 8rpx;
  height: 8rpx;
  background: #fff;
  border-radius: 50%;
  -webkit-animation: fireworkAnimation-data-v-557dc19a 1.5s ease-out infinite;
          animation: fireworkAnimation-data-v-557dc19a 1.5s ease-out infinite;
  -webkit-transform-origin: 0 0;
          transform-origin: 0 0;
}
.winning-icon.data-v-557dc19a {
  margin: 40rpx 0 20rpx;
  -webkit-animation: bounce-data-v-557dc19a 1s ease-in-out infinite;
          animation: bounce-data-v-557dc19a 1s ease-in-out infinite;
}
.icon-text.data-v-557dc19a {
  font-size: 120rpx;
  line-height: 1;
}
.congratulations.data-v-557dc19a {
  margin-bottom: 30rpx;
}
.congrats-text.data-v-557dc19a {
  color: #fff;
  font-size: 48rpx;
  font-weight: bold;
  text-shadow: 2rpx 2rpx 4rpx rgba(0, 0, 0, 0.3);
}
.prize-info.data-v-557dc19a {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 15rpx;
  padding: 30rpx;
  margin: 30rpx 0;
}
.prize-name.data-v-557dc19a {
  color: #333;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}
.prize-desc.data-v-557dc19a {
  color: #666;
  font-size: 28rpx;
}
.draw-time.data-v-557dc19a {
  margin: 20rpx 0 40rpx;
}
.draw-time text.data-v-557dc19a {
  color: rgba(255, 255, 255, 0.8);
  font-size: 24rpx;
}
.claim-instruction.data-v-557dc19a {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 15rpx;
  padding: 30rpx;
  margin: 30rpx 0;
}
.instruction-title.data-v-557dc19a {
  color: #333;
  font-size: 30rpx;
  font-weight: bold;
  margin-bottom: 15rpx;
}
.instruction-content.data-v-557dc19a {
  color: #666;
  font-size: 26rpx;
  line-height: 1.5;
}
.wechat-qrcode.data-v-557dc19a {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 15rpx;
  padding: 30rpx;
  margin: 30rpx 0;
  text-align: center;
}
.qrcode-img.data-v-557dc19a {
  width: 200rpx;
  height: 200rpx;
  border-radius: 10rpx;
  margin-bottom: 15rpx;
}
.qrcode-error.data-v-557dc19a {
  color: #666;
  font-size: 24rpx;
  line-height: 1.4;
}
@-webkit-keyframes bounce-data-v-557dc19a {
0%, 20%, 50%, 80%, 100% {
    -webkit-transform: translateY(0);
            transform: translateY(0);
}
40% {
    -webkit-transform: translateY(-10rpx);
            transform: translateY(-10rpx);
}
60% {
    -webkit-transform: translateY(-5rpx);
            transform: translateY(-5rpx);
}
}
@keyframes bounce-data-v-557dc19a {
0%, 20%, 50%, 80%, 100% {
    -webkit-transform: translateY(0);
            transform: translateY(0);
}
40% {
    -webkit-transform: translateY(-10rpx);
            transform: translateY(-10rpx);
}
60% {
    -webkit-transform: translateY(-5rpx);
            transform: translateY(-5rpx);
}
}
@-webkit-keyframes fireworkAnimation-data-v-557dc19a {
0% {
    -webkit-transform: translate(0, 0) scale(1);
            transform: translate(0, 0) scale(1);
    opacity: 1;
}
100% {
    -webkit-transform: translate(100rpx, -100rpx) scale(0);
            transform: translate(100rpx, -100rpx) scale(0);
    opacity: 0;
}
}
@keyframes fireworkAnimation-data-v-557dc19a {
0% {
    -webkit-transform: translate(0, 0) scale(1);
            transform: translate(0, 0) scale(1);
    opacity: 1;
}
100% {
    -webkit-transform: translate(100rpx, -100rpx) scale(0);
            transform: translate(100rpx, -100rpx) scale(0);
    opacity: 0;
}
}
/* 无活动提示样式 */
.no-activity-tip.data-v-557dc19a {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20rpx;
  padding: 60rpx 40rpx;
  margin-bottom: 40rpx;
  text-align: center;
}
.no-activity-tip .tip-icon.data-v-557dc19a {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}
.no-activity-tip .tip-title.data-v-557dc19a {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}
.no-activity-tip .tip-desc.data-v-557dc19a {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}
/* 抽奖规则样式 */
.lottery-rules.data-v-557dc19a {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  margin-bottom: 40rpx;
}
.lottery-rules .rules-title.data-v-557dc19a {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  text-align: center;
}
.lottery-rules .rules-content.data-v-557dc19a {
  font-size: 28rpx;
  color: #666;
  line-height: 1.8;
  text-align: left;
  white-space: pre-wrap;
}
/* 抽奖次数用完提示样式 */
.no-draws-tip.data-v-557dc19a {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20rpx;
  padding: 60rpx 40rpx;
  margin-bottom: 40rpx;
  text-align: center;
}
.no-draws-tip .tip-icon.data-v-557dc19a {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}
.no-draws-tip .tip-title.data-v-557dc19a {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}
.no-draws-tip .tip-desc.data-v-557dc19a {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}
.no-draws-tip .tip-desc text.data-v-557dc19a {
  display: block;
  margin-bottom: 10rpx;
}
/* 抽奖次数信息样式 */
.draws-info.data-v-557dc19a {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}
.draws-info .info-title.data-v-557dc19a {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  text-align: center;
}
.draws-info .info-content.data-v-557dc19a {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}
.draws-info .info-item.data-v-557dc19a {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
  gap: 10rpx;
}
.draws-info .info-item .info-label.data-v-557dc19a {
  font-size: 28rpx;
  color: #666;
  font-weight: 500;
}
.draws-info .info-item .info-value.data-v-557dc19a {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}
.draws-info .info-item .info-remaining.data-v-557dc19a {
  font-size: 24rpx;
  /* 颜色通过内联样式动态设置 */
  font-weight: 500;
}
.prize-list.data-v-557dc19a {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 40rpx;
}
.prize-list .prize-title.data-v-557dc19a {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  text-align: center;
}
.prize-list .prize-items.data-v-557dc19a {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}
.prize-list .prize-item.data-v-557dc19a {
  flex: 1;
  min-width: 200rpx;
  background: #f8f9fa;
  border-radius: 10rpx;
  padding: 20rpx;
}
.prize-list .prize-item .prize-info.data-v-557dc19a {
  display: flex;
  align-items: center;
  gap: 15rpx;
}
.prize-list .prize-item .prize-icon-small.data-v-557dc19a {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}
.prize-list .prize-item .prize-icon-small .prize-image-small.data-v-557dc19a {
  width: 100%;
  height: 100%;
  border-radius: 6rpx;
}
.prize-list .prize-item .prize-icon-small .default-icon-small.data-v-557dc19a {
  font-size: 32rpx;
}
.prize-list .prize-item .prize-details.data-v-557dc19a {
  flex: 1;
  text-align: left;
}
.prize-list .prize-item .prize-name.data-v-557dc19a {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
}
.prize-list .prize-item .prize-probability.data-v-557dc19a {
  font-size: 24rpx;
  color: #666;
}
